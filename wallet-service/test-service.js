#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:8001';

// Test results storage
const testResults = [];

// Helper function to run a test
async function runTest(testName, testFunction) {
  console.log(`\n🧪 Running test: ${testName}`);
  try {
    const result = await testFunction();
    testResults.push({ name: testName, status: 'PASS', result });
    console.log(`✅ ${testName}: PASSED`);
    return result;
  } catch (error) {
    testResults.push({ name: testName, status: 'FAIL', error: error.message });
    console.log(`❌ ${testName}: FAILED - ${error.message}`);
    return null;
  }
}

// Test functions
async function testHealthEndpoint() {
  const response = await axios.get(`${BASE_URL}/health`);
  if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
  if (!response.data.success) throw new Error('Health check failed');
  return response.data;
}

async function testBanksEndpoint() {
  const response = await axios.get(`${BASE_URL}/api/v1/wallet/banks`);
  if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
  if (!response.data.success) throw new Error('Banks fetch failed');
  if (!Array.isArray(response.data.data)) throw new Error('Banks data is not an array');
  if (response.data.data.length === 0) throw new Error('No banks returned');
  return { bankCount: response.data.data.length, sampleBank: response.data.data[0] };
}

async function testWalletBalanceEndpoint() {
  const walletId = '67dc21990a5c933c2d172b11'; // Banking wallet ID from env
  const response = await axios.get(`${BASE_URL}/api/v1/wallet/balance/${walletId}`);
  if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
  if (!response.data.success) throw new Error('Balance fetch failed');
  if (typeof response.data.data.balance !== 'number') throw new Error('Balance is not a number');
  return response.data.data;
}

async function testUnauthorizedAccess() {
  try {
    await axios.post(`${BASE_URL}/api/v1/wallet/`, { businessId: 'test123' });
    throw new Error('Expected unauthorized error');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      return { message: 'Correctly returned 401 Unauthorized' };
    }
    throw error;
  }
}

async function test404Handler() {
  try {
    await axios.get(`${BASE_URL}/api/v1/wallet/nonexistent`);
    throw new Error('Expected 404 error');
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return { message: 'Correctly returned 404 Not Found', data: error.response.data };
    }
    throw error;
  }
}

async function testInvalidWalletBalance() {
  const invalidWalletId = 'invalid-wallet-id';
  try {
    await axios.get(`${BASE_URL}/api/v1/wallet/balance/${invalidWalletId}`);
    throw new Error('Expected error for invalid wallet ID');
  } catch (error) {
    if (error.response && (error.response.status === 400 || error.response.status === 404)) {
      return { message: 'Correctly handled invalid wallet ID', status: error.response.status };
    }
    throw error;
  }
}

async function testCorsHeaders() {
  const response = await axios.get(`${BASE_URL}/health`);
  const corsHeader = response.headers['access-control-allow-origin'];
  if (!corsHeader) throw new Error('CORS headers not present');
  return { corsHeader };
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Wallet Service Tests...\n');
  console.log('=' .repeat(50));

  // Run all tests
  await runTest('Health Endpoint', testHealthEndpoint);
  await runTest('Banks Endpoint', testBanksEndpoint);
  await runTest('Wallet Balance Endpoint', testWalletBalanceEndpoint);
  await runTest('Unauthorized Access Protection', testUnauthorizedAccess);
  await runTest('404 Handler', test404Handler);
  await runTest('Invalid Wallet Balance', testInvalidWalletBalance);
  await runTest('CORS Headers', testCorsHeaders);

  // Print summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(50));

  const passed = testResults.filter(t => t.status === 'PASS').length;
  const failed = testResults.filter(t => t.status === 'FAIL').length;

  console.log(`Total Tests: ${testResults.length}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`Success Rate: ${((passed / testResults.length) * 100).toFixed(1)}%`);

  if (failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.filter(t => t.status === 'FAIL').forEach(test => {
      console.log(`  - ${test.name}: ${test.error}`);
    });
  }

  console.log('\n🎉 Service Status: ' + (failed === 0 ? 'HEALTHY ✅' : 'ISSUES DETECTED ⚠️'));
  
  return { passed, failed, total: testResults.length };
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests };
